import { defineStore } from "pinia";
import { useGlobalStore } from "@/stores/global";
import { CHANEL_TYPE, AWARD_UPDATE_TYPE, CHANEL_TERMINAL } from "@/utils/config/GlobalConstant";
import { getGlobalDialog } from "@/enter/vant";
import { firstDepositBonusGuide, getUserStatus, getDownloadGuideConfig } from "@/api/user";
import { removeLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { getToday, getServerSideImageUrl } from "@/utils/core/tools";
import { getBanners } from "@/api/games";
import placeholderBannerBase64 from "@/assets/constants/homeBannerBase64";
import { KycMgr, KycState, InGameType } from "@/utils/KycMgr";
import router from "@/router";
import {
  getRankCasino,
  getRank<PERSON><PERSON>,
  getActivityBonusList,
  getAdjustmentBonusRecordList,
  getActivitySpinConfig,
  getPwaConfig,
} from "@/api/activity";
import { showToast } from "vant";
import { bannerStorageManager } from "@/utils/managers/BannerStorageManager";
import { envelopeDisplayManager } from "@/utils/managers/EnvelopeDisplayManager";
import { shouldShowPWAPrompt } from "@/utils/pwa";
// 弹窗来源
export enum POP_FORM {
  AUTO, // 可以往下弹
  CLICK, // 只弹当前
}

export const STORE_KEY_MAP = {
  HAS_SHOWN_UPGRADE_TIP: "HAS_SHOWN_UPGRADE_TIP",
  HAS_SHOWN_UPGRADE_TIP2: "HAS_SHOWN_UPGRADE_TIP2",
  RANK_LAST_ENTER_TIME: "RANK_LAST_ENTER_TIME",
  RANK_LAST_ENTER_TIME_JILI: "RANK_LAST_ENTER_TIME_JILI",
  SPIN_LAST_ENTER_TIME: "SPIN_LAST_ENTER_TIME",
};

export const useAutoPopMgrStore = defineStore("autoPopMgr", {
  state: () => ({
    // 弹窗显示状态
    showTips21Tip: false, // 21岁提示
    showVipTip: false, // VIP提示
    showEnvelopeTip: false, // 首充奖励提示
    showActivityBonusTip: false, // 活动奖励提示
    showRegisterBonusTip: false, // 注册奖励
    showLeaderBoardPopTip: false, // 排行弹窗提示
    showLeaderBoardPopJILITip: false, // JILI排行提示
    showPopupBannersTip: false, // Banner提示（可能多张）
    showSpinWheelTip: false, // 转盘
    showKycTip: false, // kyc
    showDownloadGuideTip: false, // 下载引导
    showPWAInstallTip: false, // PWA 安装引导
    showPWAUnSupportGuide: false, // PWA不支持安装引导

    // 控制页面初始化只弹一次
    hasPop: false,

    // 来源
    sourceEnvelope: POP_FORM.AUTO,
    sourceSpinWheel: POP_FORM.AUTO,

    // 弹窗数据
    envelopeInfo: {},
    popBanners: [],
    banners: [],
    leaderBoardPopInfo: {},
    leaderBoardPopJILIInfo: {},
    popActivityBonus: [],
    // 转盘活动信息 total_prize 滚动数字
    spinInfo: { is_start: 0, left_times: 5, real_left_times: 0, total_prize: 0 },
    // 奖励弹窗类型区分
    activityBonusType: -1,
    // 转盘type
    spinType: -1,
    kycInfo: {},

    pwaInfo: {} as {
      PWA_open_times?: number;
      [key: string]: any;
    },
    // 下载引导配置
    downloadGuideConfig: {} as {
      button_text?: string;
      close_type?: number;
      download_url?: string;
      icon?: string;
      pop_info?: string;
      pop_title?: string;
      slogan?: string;
      status?: string;
      channel?: string;
    },

    // Banner 图片预加载缓存
    preloadedBannerImages: {} as Record<string, string>,
    bannerImagesPreloaded: false,

    // 转盘按钮显示状态
    showTurntableBtn: false,
  }),
  getters: {
    userInfo() {
      const globalStore = useGlobalStore();
      return globalStore.userInfo ?? {};
    },
    // 渠道来源（统一转成小写比较）
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel ?? "";
    },
    isMiniChannel() {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
  },
  actions: {
    // 私有方法：获取 globalStore 实例
    _getGlobalStore() {
      return useGlobalStore();
    },

    // 私有方法：检查用户是否已登录
    _isLoggedIn(): boolean {
      return !!this._getGlobalStore().token;
    },
    // 是否需要弹出21岁提示
    isNeedShowTip21Old() {
      const isLoggedIn = this._isLoggedIn();
      const hasShown = window["showTip21Old"];

      // 如果已登录，绝对不显示，并标记为已显示
      if (isLoggedIn) {
        window["showTip21Old"] = true;
        return false;
      }

      // 如果已经显示过，不再显示
      if (hasShown) {
        return false;
      }

      // 只有在 Home 页面、未登录且未显示过才显示
      return true;
    },

    /** 是否需要弹出 VIP 升级提示 */
    isNeedShowVipTip(): boolean {
      if (!this.userInfo?.is_vip) return false;
      const isVip = parseInt(this.userInfo.is_vip);
      if (!isVip) return false;
      const currentDate = new Date();
      const day = currentDate.getDate();
      const hasTip = getLocalStorage(STORE_KEY_MAP.HAS_SHOWN_UPGRADE_TIP + this.userInfo.user_id);
      const hasTip2 = getLocalStorage(STORE_KEY_MAP.HAS_SHOWN_UPGRADE_TIP2 + this.userInfo.user_id);
      // 前半月与后半月提示互斥处理
      if (day <= 15 && !hasTip) return true;
      if (day >= 16 && !hasTip2) return true;
      return false;
    },
    /** 初始化首存红包数据 */
    async getEnvelopeData() {
      if (!this._isLoggedIn()) return;
      this.envelopeInfo = await firstDepositBonusGuide({});
    },
    /** 判断是否需要显示首充红包弹窗 */
    isNeedShowEnvelopePop(): boolean {
      // 必须登录才能显示
      if (!this._isLoggedIn()) {
        return false;
      }

      // 设置弹窗来源
      this.sourceEnvelope = POP_FORM.AUTO;

      // 检查基础条件
      const isFirstRecharge = !this.envelopeInfo?.is_first_recharge;
      const isGuideStarted =
        parseInt(this.envelopeInfo?.first_deposit_bonus_guide_is_start || "0") === 1;

      // 如果基础条件不满足，直接返回 false
      if (!isGuideStarted || !isFirstRecharge) {
        return false;
      }
      // 使用新的管理器检查显示条件
      const config = {
        isFirstRecharge,
        isGuideStarted,
        userId: this.userInfo?.user_id,
      };

      return envelopeDisplayManager.canShow(config);
    },
    /** 初始化 Banner 数据 */
    async getBanners() {
      const res = await getBanners({});
      if (res.pop_banner?.length) {
        // 先按渠道过滤
        const channelFiltered = this.filterPopBanner(res.pop_banner);
        // 再按显示次数过滤
        const countFiltered = this.filterBannerByShowCount(channelFiltered);
        this.popBanners = countFiltered.sort((a, b) => b.sort - a.sort);
        this.banners = res.banner;
        this.showTurntableBtn = res.banner.findIndex((r) => r.activity_list === "6") > -1;
        // 预加载图片
        await this.preloadBannerImages();
      }
    },

    /** 预加载 Banner 图片 */
    async preloadBannerImages(): Promise<void> {
      if (!this.popBanners?.length) return;
      if (this.bannerImagesPreloaded) return;

      // 使用 Base64 编码的占位图替代文件路径
      const defaultPlaceholder = placeholderBannerBase64;
      const TIMEOUT_MS = 8000; // 8秒超时

      const imageLoadPromises = this.popBanners.map((banner: any) => {
        return new Promise<void>((resolve) => {
          let imgUrl = banner.image;
          if (this.CHANEL_TYPE === CHANEL_TYPE.GOOGLE_PLAY) {
            if (banner.google_pop_banner) {
              imgUrl = banner.google_pop_banner;
            }
          }
          if (this.CHANEL_TYPE === CHANEL_TYPE.IOS) {
            if (banner.ios_pop_banner) {
              imgUrl = banner.ios_pop_banner;
            }
          }
          if (!/^http/.test(imgUrl)) {
            imgUrl = getServerSideImageUrl(imgUrl);
          }

          const img = new Image();
          let isResolved = false;

          const timeout = setTimeout(() => {
            if (!isResolved) {
              this.preloadedBannerImages[banner.id] = defaultPlaceholder;
              isResolved = true;
              resolve();
            }
          }, TIMEOUT_MS);

          const handleLoad = () => {
            if (!isResolved) {
              this.preloadedBannerImages[banner.id] = imgUrl;
              clearTimeout(timeout);
              isResolved = true;

              resolve();
            }
          };

          const handleError = () => {
            if (!isResolved) {
              this.preloadedBannerImages[banner.id] = defaultPlaceholder;
              clearTimeout(timeout);
              isResolved = true;
              resolve();
            }
          };

          img.onload = handleLoad;
          img.onerror = handleError;
          img.src = imgUrl;
        });
      });

      try {
        await Promise.all(imageLoadPromises);
        this.bannerImagesPreloaded = true;
      } catch (error) {
        this.bannerImagesPreloaded = true;
      }
    },
    /**
     * 过滤 Banner 数据，根据渠道和时间范围匹配
     * @param data Banner 数据数组
     * @returns 过滤后的 Banner 数组
     */
    filterPopBanner(data: any[]): any[] {
      // 早期返回：空数据检查
      if (!data?.length) return [];

      const channelType = this.CHANEL_TYPE.toLowerCase();
      const currentTime = Date.now();

      return data.filter((item) => {
        try {
          // 必要字段验证
          if (!item?.channel || !item?.start_at || !item?.end_at) {
            return false;
          }

          // 渠道匹配检查（使用 includes 替代 indexOf）
          const channel = item.channel.toLowerCase();
          if (!channel.includes(channelType)) {
            return false;
          }

          // 时间范围检查
          const startTime = new Date(item.start_at).getTime();
          const endTime = new Date(item.end_at).getTime();

          // 时间有效性验证
          if (isNaN(startTime) || isNaN(endTime)) {
            console.warn(`Banner ID ${item.id} 时间格式无效:`, {
              start_at: item.start_at,
              end_at: item.end_at,
            });
            return false;
          }

          // 时间逻辑验证
          if (startTime >= endTime) {
            console.warn(`Banner ID ${item.id} 时间逻辑错误: 开始时间不能晚于结束时间`);
            return false;
          }

          // 检查是否在有效时间范围内
          return currentTime >= startTime && currentTime <= endTime;
        } catch (error) {
          console.error(`过滤 Banner 时发生错误:`, error, item);
          return false;
        }
      });
    },

    /** 过滤 Banner 数据，根据显示次数限制 */
    filterBannerByShowCount(data: any[]): any[] {
      // 转换数据格式以适配新的管理器
      const bannerConfigs = data.map((item) => ({
        id: item.id,
        maxShows: item.show_count,
        ...item,
      }));
      return bannerStorageManager.filterShowable(bannerConfigs, this.userInfo?.user_id);
    },
    /** 判断是否需要显示 Banner 弹窗 */
    isNeedShowPopupBanner(): boolean {
      // 转换数据格式以适配新的管理器
      const bannerConfigs = this.popBanners.map((item) => ({
        id: item.id,
        maxShows: item.show_count,
        ...item,
      }));
      return bannerStorageManager.hasShowable(bannerConfigs, this.userInfo?.user_id);
    },

    /** 初始化 Casino 排行数据 */
    async getCainoYesterdaytRankData() {
      if (!this._isLoggedIn()) return;
      this.leaderBoardPopInfo = await getRankCasino({ type: 2 });
    },
    /** 判断是否需要显示 Casino 排行弹窗 */
    isNeedShowRankPop(): boolean {
      if (!this._isLoggedIn()) return false;

      const today = getToday();
      const rank = this.leaderBoardPopInfo?.list?.rank;
      const hasData = rank && rank[0]?.player_id && rank[0].player_id !== "--";

      if (!hasData) return false;

      const lastEntry = getLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME) || "";
      if (!lastEntry) return true;

      const [lastDate, count] = lastEntry.split("_");
      return lastDate !== today || (lastDate === today && parseInt(count) < 2);
    },
    /** 初始化 JILI 排行数据 */
    async getJiLiRankData() {
      if (!this._isLoggedIn()) return;
      this.leaderBoardPopJILIInfo = await getRankjili({ type: 2 });
    },
    /** 判断是否需要显示 JILI 排行弹窗 */
    isNeedShowRankPopJili(): boolean {
      if (!this._isLoggedIn()) return false;

      const today = getToday();
      const rank = this.leaderBoardPopJILIInfo?.list?.rank;
      const hasData = rank && rank[0]?.player_id && rank[0].player_id !== "--";
      if (!hasData) return false;

      const lastEntry = getLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME_JILI) || "";
      if (!lastEntry) return true;

      const [lastDate, count] = lastEntry.split("_");
      return lastDate !== today || (lastDate === today && parseInt(count) < 2);
    },
    /** 获取所有活动奖励数据 */
    async getAllActivityBonus() {
      if (!this._isLoggedIn()) return;
      // const result = await getActivityBonusList({});
      const result = {
        list: [
          {
            id: "86952019906007045",
            type: 265,
            bonus: 64,
            bonus_date: "2025-05-08",
            jili_back_coin: "160.00",
          },
          {
            id: "86952019906007045",
            type: 310,
            bonus: 1,
            bonus_date: "2025-05-08",
            jili_back_coin: "16.00",
          },
          //   {
          //     id: "86952019906007045",
          //     type: 100001,
          //     bonus: 104,
          //     bonus_date: "2025-05-08",
          //     jili_back_coin: "10.00",
          //   },
        ],
      };
      if (result?.list?.length) {
        this.popActivityBonus = this.popActivityBonus.concat(result.list);
      }
      const result2 = await getAdjustmentBonusRecordList({});
      if (result2?.list?.length) {
        this.popActivityBonus = this.popActivityBonus.concat(result2.list);
      }
    },
    /** 判断是否需要显示活动奖励弹窗 */
    isNeedShowActivityBonus(): boolean {
      if (!this._isLoggedIn()) return false;
      if (!this.popActivityBonus && !this.popActivityBonus.length) return false;
      return this.popActivityBonus?.length > 0;
    },
    /** 判断是否需要显示注册奖励弹窗 */
    isNeedShowRegisterBonus(): boolean {
      if (!this._isLoggedIn()) return false;
      const globalStore = this._getGlobalStore();
      const type = globalStore.registerAward?.type;
      const amount = globalStore.registerAward.amount;
      if ([1, 3].includes(type)) {
        this.activityBonusType = AWARD_UPDATE_TYPE.REGISTER_USER;
        return true;
      } else if (type == 2) {
        // 绑定手机号
        this.activityBonusType = AWARD_UPDATE_TYPE.REGISTER_USER;
        return true;
      }
      return false;
    },
    /**是否需要弹出转盘弹窗 */
    isNeedShowSpin(): boolean {
      if (!this._isLoggedIn()) return false;
      this.sourceSpinWheel = POP_FORM.AUTO;
      let currentDate = new Date();
      let currentDateStr = `${currentDate.getFullYear()}-${
        currentDate.getMonth() + 1
      }-${currentDate.getDate()}`;
      let lastEnterDateStr =
        getLocalStorage(STORE_KEY_MAP.SPIN_LAST_ENTER_TIME + this.userInfo?.user_id) || "";
      if (lastEnterDateStr !== currentDateStr && this.spinInfo?.is_start == 1) {
        //修改逻辑 不自动弹出
        // return true;
      }
      if (this.spinInfo?.real_left_times > 0 && this.spinInfo?.is_start == 1) {
        return true;
      }

      return false;
    },
    // 打开大转盘
    async openSpinWheel() {
      if (!this._isLoggedIn()) {
        router.replace("/login");
        return false;
      }
      const res = await getActivitySpinConfig();
      if (res && res.is_start == 1) {
        this.spinInfo = res;
        this.sourceSpinWheel = POP_FORM.CLICK;
        this.showSpinWheelTip = true;
      } else {
        showToast("The activity has ended!");
      }
    },
    // 获取转盘活动是否开启以及剩余抽奖次数
    async getSpinInfo() {
      if (!this._isLoggedIn()) return false;
      const res = await getActivitySpinConfig();
      this.spinInfo = res;
    },
    //KYC 展示
    isNeedShowPopKYC() {
      if (!this._isLoggedIn()) return false;
      // 如果是非web 渠道，不弹出
      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        return false;
      }
      // 简版时，不弹出
      if (this.kycInfo.is_full === 0) {
        return false;
      }
      // 审核中或者审核完成后 ，不弹出
      if ([KycState.COMPLETE, KycState.REVIEWING].includes(this.kycInfo?.status)) {
        return false;
      }
      if ([KycState.NO_VERIFY, KycState.REJECTED].includes(this.kycInfo?.status)) {
        return true;
      }
      return false;
    },
    async getKycInfo() {
      if (!this._isLoggedIn()) return;
      const res = await KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
      this.kycInfo = res;
      return res;
    },

    /** 检查渠道是否匹配 */
    isChannelMatched(configChannel: string): boolean {
      const currentChannel = this.CHANEL_TYPE;
      const currentChannelTerminal = CHANEL_TERMINAL[currentChannel];

      // 仅支持匹配渠道终端数字（如 "128", "64", "8"）
      // 支持逗号分隔的多渠道配置（如 "128,64"）
      const configChannels = configChannel.split(",").map((ch) => ch.trim());

      for (const channel of configChannels) {
        // 检查是否匹配渠道终端数字
        if (channel === String(currentChannelTerminal)) {
          return true;
        }
      }
      return false;
    },

    /** 获取下载引导配置 */
    async getDownloadGuideConfig() {
      if (!this._isLoggedIn()) return false;
      try {
        // 调用API获取配置
        const response = await getDownloadGuideConfig();
        // 处理不同的响应格式
        const resData = response?.data || response;

        // 数据验证和处理
        if (resData && Array.isArray(resData)) {
          // 新的响应结构：data.guide 是一个数组
          // 取第一个配置项
          const configData =
            resData.find((t) => this.isChannelMatched(t.channel) && t.status === "1") || {};
          this.downloadGuideConfig = {
            ...configData,
            download_url: configData.download_url + "?channel=" + CHANEL_TERMINAL[this.CHANEL_TYPE],
          };
        } else {
          this.downloadGuideConfig = {};
        }
      } catch (error) {
        // 设置默认配置
        this.downloadGuideConfig = {};
      }
    },

    /** 判断是否需要显示下载引导弹窗 */
    isNeedShowDownloadGuide(): boolean {
      // if (!this._isLoggedIn()) return false;

      // 检查配置是否启用
      if (!this.downloadGuideConfig?.show) {
        return false;
      }

      // 检查状态是否启用
      if (this.downloadGuideConfig.status !== "1") {
        return false;
      }

      // 检查渠道匹配
      if (this.downloadGuideConfig.channel) {
        const isChannelMatched = this.isChannelMatched(this.downloadGuideConfig.channel);
        if (!isChannelMatched) {
          return false;
        }
      }

      // 检查必要的配置字段是否存在
      if (!this.downloadGuideConfig.download_url || !this.downloadGuideConfig.slogan) {
        return false;
      }

      return true;
    },

    async getPwaConfigInfo() {
      const res = await getPwaConfig();
      try {
        this.pwaInfo = JSON.parse(res?.value?.[1]?.value) || {};
      } catch {
        this.pwaInfo = {};
      }
    },

    /** 检查是否需要显示 PWA 安装引导 */
    isNeedShowPWAInstall(): boolean {
      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        return false;
      }

      // 从 pwaInfo 中获取每日最大弹窗次数，如果没有配置则使用默认值 3
      const maxDailyPrompts = this.pwaInfo?.PWA_open_times || 3;

      return shouldShowPWAPrompt(maxDailyPrompts);
    },

    // 关闭所有弹窗
    closeAllPopups() {
      // 关闭所有弹窗显示状态
      this.showTips21Tip = false;
      this.showVipTip = false;
      this.showEnvelopeTip = false;
      this.showActivityBonusTip = false;
      this.showRegisterBonusTip = false;
      this.showLeaderBoardPopTip = false;
      this.showLeaderBoardPopJILITip = false;
      this.showPopupBannersTip = false;
      this.showSpinWheelTip = false;
      this.showKycTip = false;
      this.showDownloadGuideTip = false;
      this.showPWAInstallTip = false;
      this.showPWAUnSupportGuide = false;
    },
  },
});
